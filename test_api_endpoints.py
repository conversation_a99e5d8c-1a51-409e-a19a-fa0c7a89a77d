#!/usr/bin/env python3
"""
Test all API endpoints to verify the system is working correctly.
"""

import requests
import json
import time
from datetime import datetime

def test_endpoint(method, url, data=None, timeout=10):
    """Test a single API endpoint."""
    try:
        print(f"\n🔍 Testing {method} {url}")
        
        if method.upper() == 'GET':
            response = requests.get(url, timeout=timeout)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, timeout=timeout)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
        
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            try:
                json_response = response.json()
                print(f"   ✅ Success")
                if isinstance(json_response, dict):
                    for key, value in list(json_response.items())[:3]:  # Show first 3 keys
                        if isinstance(value, str) and len(value) > 50:
                            print(f"   {key}: {value[:50]}...")
                        else:
                            print(f"   {key}: {value}")
                return True
            except json.JSONDecodeError:
                print(f"   ✅ Success (non-JSON response)")
                print(f"   Response: {response.text[:100]}...")
                return True
        else:
            print(f"   ❌ Failed")
            print(f"   Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection Error - Server not running or wrong port")
        return False
    except requests.exceptions.Timeout:
        print(f"   ❌ Timeout - Server took too long to respond")
        return False
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False

def main():
    """Test all API endpoints."""
    print("🧪 Meeting Intelligence Agent - API Endpoint Testing")
    print("=" * 60)
    
    # Base URL - adjust port if needed
    base_url = "http://localhost:8002"
    
    # Test endpoints
    endpoints = [
        ("GET", f"{base_url}/", "Root endpoint"),
        ("GET", f"{base_url}/health", "Health check"),
        ("GET", f"{base_url}/agent/status", "Agent status"),
        ("GET", f"{base_url}/agent/config", "Agent configuration"),
        ("GET", f"{base_url}/agent/summary", "Latest summaries"),
        ("GET", f"{base_url}/agent/feedback/stats", "Feedback statistics"),
        ("POST", f"{base_url}/agent/trigger", "Trigger workflow"),
        ("POST", f"{base_url}/agent/start-scheduler", "Start scheduler"),
    ]
    
    results = {}
    
    print(f"\n🌐 Testing API server at: {base_url}")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    for method, url, description in endpoints:
        print(f"\n📋 {description}")
        success = test_endpoint(method, url)
        results[description] = success
        
        # Small delay between requests
        time.sleep(0.5)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if success:
            passed += 1
    
    print(f"\nResults: {passed}/{total} endpoints working")
    
    if passed == total:
        print("🎉 All API endpoints are working correctly!")
    elif passed > 0:
        print("⚠️ Some endpoints are working. Check failed ones above.")
    else:
        print("❌ No endpoints are working. Check if server is running.")
    
    # Provide helpful information
    print(f"\n🔧 HELPFUL INFORMATION:")
    print(f"   API Server URL: {base_url}")
    print(f"   Interactive Docs: {base_url}/docs")
    print(f"   Health Check: {base_url}/health")
    print(f"   Manual Trigger: POST {base_url}/agent/trigger")
    
    print(f"\n📝 CURL COMMANDS FOR TESTING:")
    print(f"   curl {base_url}/health")
    print(f"   curl {base_url}/agent/status")
    print(f"   curl -X POST {base_url}/agent/trigger")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
